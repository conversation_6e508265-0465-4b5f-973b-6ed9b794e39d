#[cfg(test)]
mod currency_tests {
    use super::*;

    fn create_test_currency(id: &str) -> Currency {
        Currency {
            _id: id.to_string(),
            created_at: 0,
            updated_at: 0,
            deleted_at: 0,
            name_or_id: "USD".to_string(),
            contract_id: "0xcontract123".to_string(),
            symbol_name: "USD".to_string(),
            contract_type: "ERC20".to_string(),
            unit: 18,
            exchange_rate: 1.0,
        }
    }

    #[glue::test]
    fn test_insert_currency() {
        let mut db = VCloudDB::new();
        let currency = create_test_currency("test_currency_1");
        let currency_json = serde_json::to_string(&currency).unwrap();
        
        let result = db.insert_currency(currency_json);
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "test_currency_1");
        
        // Verify currency was inserted
        assert!(db.currencies.contains(&"test_currency_1".to_string()));
    }

    #[glue::test]
    fn test_get_currency() {
        let mut db = VCloudDB::new();
        let currency = create_test_currency("get_test_currency");
        let currency_json = serde_json::to_string(&currency).unwrap();
        
        // Insert currency first
        db.insert_currency(currency_json).unwrap();
        
        // Test get
        let result = db.get_currency("get_test_currency".to_string());
        assert!(result.is_ok());
        
        let retrieved_currency: Currency = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(retrieved_currency._id, "get_test_currency");
        assert_eq!(retrieved_currency.name_or_id, "USD");
    }

    #[glue::test]
    fn test_insert_many_currency() {
        let mut db = VCloudDB::new();
        let currencies = vec![
            create_test_currency("batch_currency_1"),
            create_test_currency("batch_currency_2"),
        ];
        let currencies_json = serde_json::to_string(&currencies).unwrap();
        
        let result = db.insert_many_currency(currencies_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.created, 2);
        assert_eq!(batch_result.errors.len(), 0);
        
        // Verify currencies were inserted
        assert!(db.currencies.contains(&"batch_currency_1".to_string()));
        assert!(db.currencies.contains(&"batch_currency_2".to_string()));
    }

    #[glue::test]
    fn test_update_currency() {
        let mut db = VCloudDB::new();
        let mut currency = create_test_currency("update_test_currency");
        let currency_json = serde_json::to_string(&currency).unwrap();
        
        // Insert currency first
        db.insert_currency(currency_json).unwrap();
        
        // Update currency
        currency.name_or_id = "EUR".to_string();
        currency.exchange_rate = 0.85;
        let updated_json = serde_json::to_string(&currency).unwrap();
        
        let result = db.update_currency(updated_json);
        assert!(result.is_ok());
        
        // Verify update
        let retrieved = db.get_currency("update_test_currency".to_string()).unwrap();
        let retrieved_currency: Currency = serde_json::from_str(&retrieved).unwrap();
        assert_eq!(retrieved_currency.name_or_id, "EUR");
        assert_eq!(retrieved_currency.exchange_rate, 0.85);
    }

    #[glue::test]
    fn test_find_currency() {
        let mut db = VCloudDB::new();
        let currency1 = create_test_currency("find_test_1");
        let mut currency2 = create_test_currency("find_test_2");
        currency2.name_or_id = "EUR".to_string();
        
        // Insert currencies
        db.insert_currency(serde_json::to_string(&currency1).unwrap()).unwrap();
        db.insert_currency(serde_json::to_string(&currency2).unwrap()).unwrap();
        
        // Find currencies with name_or_id filter
        let query_params = CurrencyQueryParams {
            ids: None,
            name_or_id: Some("USD".to_string()),
            contract_id: None,
            symbol_name: None,
            contract_type: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let query_json = serde_json::to_string(&query_params).unwrap();
        
        let result = db.find_currency(query_json);
        assert!(result.is_ok());
        
        let found_currencies: Vec<Currency> = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(found_currencies.len(), 1);
        assert_eq!(found_currencies[0]._id, "find_test_1");
    }

    #[glue::test]
    fn test_count_currency() {
        let mut db = VCloudDB::new();
        let currency1 = create_test_currency("count_test_1");
        let currency2 = create_test_currency("count_test_2");
        
        // Insert currencies
        db.insert_currency(serde_json::to_string(&currency1).unwrap()).unwrap();
        db.insert_currency(serde_json::to_string(&currency2).unwrap()).unwrap();
        
        // Count all currencies
        let query_params = CurrencyQueryParams {
            ids: None,
            name_or_id: None,
            contract_id: None,
            symbol_name: None,
            contract_type: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let query_json = serde_json::to_string(&query_params).unwrap();
        
        let result = db.count_currency(query_json);
        assert!(result.is_ok());
        
        let count: u64 = result.unwrap().parse().unwrap();
        assert_eq!(count, 2);
    }

    #[glue::test]
    fn test_delete_currency() {
        let mut db = VCloudDB::new();
        let currency = create_test_currency("delete_test_currency");
        let currency_json = serde_json::to_string(&currency).unwrap();
        
        // Insert currency first
        db.insert_currency(currency_json).unwrap();
        assert!(db.currencies.contains(&"delete_test_currency".to_string()));
        
        // Delete currency
        let query_params = CurrencyQueryParams {
            ids: Some(vec!["delete_test_currency".to_string()]),
            name_or_id: None,
            contract_id: None,
            symbol_name: None,
            contract_type: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let query_json = serde_json::to_string(&query_params).unwrap();
        
        let result = db.delete_currency(query_json);
        assert!(result.is_ok());
        
        // Verify currency was deleted
        assert!(!db.currencies.contains(&"delete_test_currency".to_string()));
    }

    #[glue::test]
    fn test_delete_many_currency() {
        let mut db = VCloudDB::new();
        let currency1 = create_test_currency("delete_many_1");
        let currency2 = create_test_currency("delete_many_2");
        
        // Insert currencies
        db.insert_currency(serde_json::to_string(&currency1).unwrap()).unwrap();
        db.insert_currency(serde_json::to_string(&currency2).unwrap()).unwrap();
        
        // Delete currencies with name_or_id filter
        let query_params = CurrencyQueryParams {
            ids: None,
            name_or_id: Some("USD".to_string()),
            contract_id: None,
            symbol_name: None,
            contract_type: None,
            created_at_start: None,
            created_at_end: None,
            updated_at_start: None,
            updated_at_end: None,
            offset: None,
            limit: None,
            sort_by: None,
            sort_desc: None,
        };
        let query_json = serde_json::to_string(&query_params).unwrap();
        
        let result = db.delete_many_currency(query_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.deleted, 2);
        
        // Verify currencies were deleted
        assert!(!db.currencies.contains(&"delete_many_1".to_string()));
        assert!(!db.currencies.contains(&"delete_many_2".to_string()));
    }

    #[glue::test]
    fn test_update_many_currency() {
        let mut db = VCloudDB::new();
        let currency1 = create_test_currency("update_many_1");
        let currency2 = create_test_currency("update_many_2");
        
        // Insert currencies
        db.insert_currency(serde_json::to_string(&currency1).unwrap()).unwrap();
        db.insert_currency(serde_json::to_string(&currency2).unwrap()).unwrap();
        
        // Update currencies
        let update_params = CurrencyUpdate {
            filter: CurrencyQueryParams {
                ids: None,
                name_or_id: Some("USD".to_string()),
                contract_id: None,
                symbol_name: None,
                contract_type: None,
                created_at_start: None,
                created_at_end: None,
                updated_at_start: None,
                updated_at_end: None,
                offset: None,
                limit: None,
                sort_by: None,
                sort_desc: None,
            },
            update_data: serde_json::json!({
                "exchangeRate": 1.2
            }),
        };
        let update_json = serde_json::to_string(&update_params).unwrap();
        
        let result = db.update_many_currency(update_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.updated, 2);
        
        // Verify updates
        let retrieved1 = db.get_currency("update_many_1".to_string()).unwrap();
        let currency1: Currency = serde_json::from_str(&retrieved1).unwrap();
        assert_eq!(currency1.exchange_rate, 1.2);
    }

    #[glue::test]
    fn test_bulk_write_currency() {
        let mut db = VCloudDB::new();
        
        // Test bulk write with insert operation
        let operations = vec![
            CurrencyBulkWriteOperation {
                operation_type: "insert".to_string(),
                filter: None,
                data: Some(serde_json::to_value(create_test_currency("bulk_insert_1")).unwrap()),
            },
        ];
        let operations_json = serde_json::to_string(&operations).unwrap();
        
        let result = db.bulk_write_currency(operations_json);
        assert!(result.is_ok());
        
        let batch_result: BatchResult = serde_json::from_str(&result.unwrap()).unwrap();
        assert_eq!(batch_result.created, 1);
        
        // Verify currency was inserted
        assert!(db.currencies.contains(&"bulk_insert_1".to_string()));
    }
}
